<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RobloxDevX - Custom Systems & Builds</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>

    <!-- Google APIs -->
    <script src="https://apis.google.com/js/api.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header/Profile Section -->
        <header class="profile-section">
            <div class="avatar">
                <img src="https://via.placeholder.com/120x120/ff4444/ffffff?text=RDX" alt="RobloxDevX Avatar" id="avatar-img">
            </div>
            <h1 class="username">RobloxDevX</h1>
            <p class="tagline">Selling Custom Systems & Builds for Roblox</p>
            <div class="social-links">
                <a href="#" class="social-link discord" title="Discord">
                    <i class="fab fa-discord"></i>
                </a>
                <a href="#" class="social-link github" title="GitHub">
                    <i class="fab fa-github"></i>
                </a>
                <a href="#" class="social-link roblox" title="Roblox">
                    <i class="fas fa-gamepad"></i>
                </a>
                <a href="#" class="social-link kofi" title="Ko-fi">
                    <i class="fas fa-coffee"></i>
                </a>
            </div>

            <!-- Authentication Section -->
            <div class="auth-section" id="auth-section">
                <div class="auth-buttons" id="auth-buttons">
                    <button class="btn btn-primary" id="login-btn">Login</button>
                    <button class="btn btn-secondary" id="signup-btn">Sign Up</button>
                </div>
                <div class="user-info" id="user-info" style="display: none;">
                    <span class="welcome-text">Welcome, <span id="user-name"></span>!</span>
                    <button class="btn btn-primary" id="dashboard-btn">Dashboard</button>
                    <button class="btn btn-outline" id="logout-btn">Logout</button>
                </div>
            </div>
        </header>

        <!-- Featured Builds Section -->
        <section class="featured-section">
            <h2 class="section-title">Featured Systems & Builds</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/300x200/4444ff/ffffff?text=Combat+System" alt="Combat System V2">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Combat System V2</h3>
                        <p class="product-description">Advanced PvP combat system with combos, blocking, and special abilities</p>
                        <div class="product-actions">
                            <button class="btn btn-primary">View Details</button>
                            <button class="btn btn-secondary">Buy Now - $25</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/300x200/ff4444/ffffff?text=Admin+Panel" alt="Admin Panel">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Admin Panel Pro</h3>
                        <p class="product-description">Complete admin system with GUI, commands, and permission levels</p>
                        <div class="product-actions">
                            <button class="btn btn-primary">View Details</button>
                            <button class="btn btn-secondary">Buy Now - $35</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/300x200/44ff44/ffffff?text=Economy+System" alt="Economy System">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Economy System</h3>
                        <p class="product-description">Full economy with shops, currency, trading, and data persistence</p>
                        <div class="product-actions">
                            <button class="btn btn-primary">View Details</button>
                            <button class="btn btn-secondary">Buy Now - $40</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/300x200/ff8844/ffffff?text=Map+Builder" alt="Map Builder">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Map Builder Tool</h3>
                        <p class="product-description">In-game map creation tool with save/load and sharing features</p>
                        <div class="product-actions">
                            <button class="btn btn-primary">View Details</button>
                            <button class="btn btn-secondary">Buy Now - $30</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Commissions Section -->
        <section class="commissions-section">
            <h2 class="section-title">Custom Commissions</h2>
            <div class="commission-content">
                <p class="commission-text">Need something specific? I create custom systems tailored to your game's needs.</p>
                <div class="commission-features">
                    <div class="feature">
                        <i class="fas fa-code"></i>
                        <span>Custom Scripting</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-cogs"></i>
                        <span>System Integration</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-support"></i>
                        <span>Ongoing Support</span>
                    </div>
                </div>
                <button class="btn btn-accent commission-btn">Request Custom Work</button>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="testimonials-section">
            <h2 class="section-title">Trusted by 500+ Developers</h2>
            <div class="testimonials-grid">
                <div class="testimonial">
                    <p>"Amazing work! The combat system transformed my game completely."</p>
                    <span class="testimonial-author">- GameDev123</span>
                </div>
                <div class="testimonial">
                    <p>"Professional, fast delivery, and excellent support. Highly recommended!"</p>
                    <span class="testimonial-author">- RobloxStudio_Pro</span>
                </div>
                <div class="testimonial">
                    <p>"Best Roblox developer I've worked with. Clean code and great documentation."</p>
                    <span class="testimonial-author">- DevMaster99</span>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#terms">Terms of Service</a>
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#refunds">Refund Policy</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link"><i class="fab fa-discord"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="social-link"><i class="fas fa-gamepad"></i></a>
                </div>
                <p class="footer-text">© 2024 RobloxDevX. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content auth-modal">
            <div class="modal-header">
                <h2 id="auth-modal-title">Login</h2>
                <button class="modal-close" id="auth-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="auth-form">
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" required>
                    </div>
                    <div class="form-group" id="confirm-password-group" style="display: none;">
                        <label for="confirm-password">Confirm Password:</label>
                        <input type="password" id="confirm-password">
                    </div>
                    <button type="submit" class="btn btn-accent" id="auth-submit">Login</button>
                </form>
                <div class="auth-divider">
                    <span>or</span>
                </div>
                <button class="btn btn-google" id="google-signin">
                    <i class="fab fa-google"></i> Continue with Google
                </button>
                <p class="auth-switch">
                    <span id="auth-switch-text">Don't have an account?</span>
                    <a href="#" id="auth-switch-link">Sign up</a>
                </p>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="purchase.js"></script>
    <script src="script.js"></script>
</body>
</html>
