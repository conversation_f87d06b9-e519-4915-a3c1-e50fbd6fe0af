// Authentication System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isSignupMode = false;
        this.initializeAuth();
        this.setupEventListeners();
    }

    initializeAuth() {
        // Listen for authentication state changes
        auth.onAuthStateChanged((user) => {
            this.currentUser = user;
            this.updateUI();
            
            if (user) {
                console.log('User signed in:', user.email);
                this.createUserDocument(user);
            } else {
                console.log('User signed out');
            }
        });
    }

    setupEventListeners() {
        // Modal controls
        document.getElementById('login-btn').addEventListener('click', () => this.showAuthModal('login'));
        document.getElementById('signup-btn').addEventListener('click', () => this.showAuthModal('signup'));
        document.getElementById('logout-btn').addEventListener('click', () => this.signOut());
        document.getElementById('dashboard-btn').addEventListener('click', () => this.goToDashboard());
        
        // Modal close
        document.getElementById('auth-modal-close').addEventListener('click', () => this.hideAuthModal());
        document.getElementById('auth-modal').addEventListener('click', (e) => {
            if (e.target.id === 'auth-modal') this.hideAuthModal();
        });
        
        // Form submission
        document.getElementById('auth-form').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Mode switching
        document.getElementById('auth-switch-link').addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleAuthMode();
        });
        
        // Google Sign-in
        document.getElementById('google-signin').addEventListener('click', () => this.signInWithGoogle());
    }

    showAuthModal(mode = 'login') {
        this.isSignupMode = mode === 'signup';
        this.updateModalUI();
        document.getElementById('auth-modal').style.display = 'flex';
        setTimeout(() => {
            document.getElementById('auth-modal').style.opacity = '1';
        }, 10);
    }

    hideAuthModal() {
        document.getElementById('auth-modal').style.opacity = '0';
        setTimeout(() => {
            document.getElementById('auth-modal').style.display = 'none';
            this.resetForm();
        }, 300);
    }

    updateModalUI() {
        const title = document.getElementById('auth-modal-title');
        const submitBtn = document.getElementById('auth-submit');
        const confirmPasswordGroup = document.getElementById('confirm-password-group');
        const switchText = document.getElementById('auth-switch-text');
        const switchLink = document.getElementById('auth-switch-link');

        if (this.isSignupMode) {
            title.textContent = 'Sign Up';
            submitBtn.textContent = 'Create Account';
            confirmPasswordGroup.style.display = 'block';
            switchText.textContent = 'Already have an account?';
            switchLink.textContent = 'Login';
        } else {
            title.textContent = 'Login';
            submitBtn.textContent = 'Login';
            confirmPasswordGroup.style.display = 'none';
            switchText.textContent = "Don't have an account?";
            switchLink.textContent = 'Sign up';
        }
    }

    toggleAuthMode() {
        this.isSignupMode = !this.isSignupMode;
        this.updateModalUI();
        this.resetForm();
    }

    resetForm() {
        document.getElementById('auth-form').reset();
        this.clearErrors();
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        this.clearErrors();

        try {
            if (this.isSignupMode) {
                // Validate passwords match
                if (password !== confirmPassword) {
                    throw new Error('Passwords do not match');
                }
                
                if (password.length < 6) {
                    throw new Error('Password must be at least 6 characters');
                }

                await this.signUp(email, password);
            } else {
                await this.signIn(email, password);
            }
            
            this.hideAuthModal();
        } catch (error) {
            this.showError(error.message);
        }
    }

    async signUp(email, password) {
        const userCredential = await auth.createUserWithEmailAndPassword(email, password);
        
        // Send email verification
        await userCredential.user.sendEmailVerification();
        
        this.showSuccess('Account created! Please check your email to verify your account.');
        return userCredential.user;
    }

    async signIn(email, password) {
        const userCredential = await auth.signInWithEmailAndPassword(email, password);
        
        if (!userCredential.user.emailVerified) {
            this.showWarning('Please verify your email address. Check your inbox for a verification link.');
        }
        
        return userCredential.user;
    }

    async signInWithGoogle() {
        try {
            const provider = new firebase.auth.GoogleAuthProvider();
            provider.addScope('email');
            provider.addScope('profile');
            
            const result = await auth.signInWithPopup(provider);
            this.hideAuthModal();
            return result.user;
        } catch (error) {
            this.showError(error.message);
        }
    }

    async signOut() {
        try {
            await auth.signOut();
            // Redirect to home if on dashboard
            if (window.location.pathname.includes('dashboard')) {
                window.location.href = 'index.html';
            }
        } catch (error) {
            console.error('Sign out error:', error);
        }
    }

    async createUserDocument(user) {
        try {
            const userRef = db.collection('users').doc(user.uid);
            const userDoc = await userRef.get();
            
            if (!userDoc.exists) {
                await userRef.set({
                    email: user.email,
                    displayName: user.displayName || user.email.split('@')[0],
                    photoURL: user.photoURL || null,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    purchases: [],
                    totalSpent: 0
                });
            }
        } catch (error) {
            console.error('Error creating user document:', error);
        }
    }

    updateUI() {
        const authButtons = document.getElementById('auth-buttons');
        const userInfo = document.getElementById('user-info');
        const userName = document.getElementById('user-name');

        if (this.currentUser) {
            authButtons.style.display = 'none';
            userInfo.style.display = 'flex';
            userName.textContent = this.currentUser.displayName || this.currentUser.email.split('@')[0];
        } else {
            authButtons.style.display = 'flex';
            userInfo.style.display = 'none';
        }
    }

    goToDashboard() {
        window.location.href = 'dashboard.html';
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showWarning(message) {
        this.showMessage(message, 'warning');
    }

    showMessage(message, type = 'info') {
        // Remove existing messages
        const existingMessage = document.querySelector('.auth-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-message auth-message-${type}`;
        messageDiv.textContent = message;

        const modalBody = document.querySelector('#auth-modal .modal-body');
        modalBody.insertBefore(messageDiv, modalBody.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    clearErrors() {
        const existingMessage = document.querySelector('.auth-message');
        if (existingMessage) {
            existingMessage.remove();
        }
    }

    // Utility methods
    isAuthenticated() {
        return !!this.currentUser;
    }

    requireAuth() {
        if (!this.isAuthenticated()) {
            this.showAuthModal('login');
            return false;
        }
        return true;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize authentication manager
const authManager = new AuthManager();

// Export for global access
window.authManager = authManager;
