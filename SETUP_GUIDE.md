# RobloxDevX Portfolio Setup Guide

This guide will help you set up the authentication and Google API integration for your Roblox developer portfolio site.

## 🔥 Firebase Setup

### 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `roblox-portfolio` (or your preferred name)
4. Enable Google Analytics (optional)
5. Click "Create project"

### 2. Enable Authentication

1. In your Firebase project, go to **Authentication** > **Sign-in method**
2. Enable the following providers:
   - **Email/Password**: Click and toggle "Enable"
   - **Google**: Click, toggle "Enable", and add your project's domain

### 3. Set up Firestore Database

1. Go to **Firestore Database** > **Create database**
2. Choose "Start in test mode" (you can secure it later)
3. Select a location close to your users

### 4. Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll down to "Your apps" and click **Web** icon (`</>`)
3. Register your app with a nickname
4. Copy the `firebaseConfig` object
5. Replace the config in `config.js`:

```javascript
const firebaseConfig = {
    apiKey: "your-actual-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789012",
    appId: "your-actual-app-id"
};
```

## 🔧 Google Cloud Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or use existing one
3. Note your Project ID

### 2. Enable Required APIs

1. Go to **APIs & Services** > **Library**
2. Search and enable:
   - **Google Drive API**
   - **Gmail API**

### 3. Create Credentials

#### API Key:
1. Go to **APIs & Services** > **Credentials**
2. Click **+ CREATE CREDENTIALS** > **API key**
3. Copy the API key
4. Click **RESTRICT KEY** and add your domain

#### OAuth 2.0 Client ID:
1. Click **+ CREATE CREDENTIALS** > **OAuth client ID**
2. Choose **Web application**
3. Add authorized origins:
   - `http://localhost` (for testing)
   - `https://yourdomain.com` (your actual domain)
4. Copy the Client ID

### 4. Update Configuration

Replace in `config.js`:

```javascript
const GOOGLE_CONFIG = {
    API_KEY: 'your-actual-google-api-key',
    CLIENT_ID: 'your-actual-client-id.apps.googleusercontent.com',
    // ... rest stays the same
};
```

## 📁 Google Drive Setup

### 1. Create Asset Folder

1. Go to [Google Drive](https://drive.google.com/)
2. Create a folder called "Roblox Assets"
3. Upload your asset files (ZIP files recommended)

### 2. Get File IDs

For each asset file:
1. Right-click the file > **Get link**
2. Copy the file ID from the URL:
   `https://drive.google.com/file/d/FILE_ID_HERE/view`
3. Update the `driveFileId` in `config.js` for each product

### 3. Set Permissions

1. Right-click your asset folder > **Share**
2. Change to "Anyone with the link can view"
3. Copy the folder link for backup

## 📧 Gmail API Setup

### 1. Enable Gmail API

1. In Google Cloud Console, ensure Gmail API is enabled
2. The OAuth consent screen should include Gmail scope

### 2. Test Email Sending

The system will automatically request Gmail permissions when a user makes a purchase.

## 🛡️ Security Configuration

### 1. Firebase Security Rules

Update Firestore rules in Firebase Console:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Purchases can be read by the owner, written by authenticated users
    match /purchases/{purchaseId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
}
```

### 2. API Key Restrictions

1. In Google Cloud Console > **Credentials**
2. Edit your API key
3. Under **API restrictions**, select "Restrict key"
4. Choose only the APIs you need:
   - Google Drive API
   - Gmail API

### 3. OAuth Consent Screen

1. Go to **OAuth consent screen**
2. Fill in required information:
   - App name: "RobloxDevX Portfolio"
   - User support email: your email
   - Developer contact: your email
3. Add scopes:
   - `https://www.googleapis.com/auth/drive.file`
   - `https://www.googleapis.com/auth/gmail.send`

## 🚀 Deployment

### 1. Update Domain Settings

1. **Firebase**: Add your domain to authorized domains
2. **Google Cloud**: Add your domain to OAuth origins
3. **Update config.js**: Replace localhost with your actual domain

### 2. Test the System

1. Create a test account
2. Try purchasing an asset
3. Check if email is sent
4. Verify download links work

## 🔍 Troubleshooting

### Common Issues:

1. **"API key not valid"**: Check API key restrictions and enabled APIs
2. **"Unauthorized domain"**: Add your domain to OAuth settings
3. **"Permission denied"**: Check Firestore security rules
4. **Email not sending**: Verify Gmail API is enabled and user has granted permissions

### Testing Locally:

1. Use `http://localhost:3000` or similar for local testing
2. Add localhost to all OAuth settings
3. Test with a real Gmail account

## 📞 Support

If you need help with setup:
1. Check the browser console for error messages
2. Verify all API keys and IDs are correct
3. Ensure all required APIs are enabled
4. Test with a fresh browser session

## 🎯 Next Steps

After setup:
1. Customize the email templates in `config.js`
2. Add your actual asset files to Google Drive
3. Update product information and pricing
4. Test the complete purchase flow
5. Deploy to your hosting platform

---

**Important**: Keep your API keys and configuration secure. Never commit them to public repositories!
