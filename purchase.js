// Purchase and Google API Integration System
class PurchaseManager {
    constructor() {
        this.isGoogleApiLoaded = false;
        this.isGoogleApiSignedIn = false;
        this.initializeGoogleApi();
    }

    async initializeGoogleApi() {
        try {
            await new Promise((resolve) => {
                gapi.load('auth2:client', resolve);
            });

            await gapi.client.init({
                apiKey: GOOGLE_CONFIG.API_KEY,
                clientId: GOOGLE_CONFIG.CLIENT_ID,
                discoveryDocs: GOOGLE_CONFIG.DISCOVERY_DOCS,
                scope: GOOGLE_CONFIG.SCOPES
            });

            this.isGoogleApiLoaded = true;
            console.log('Google API initialized successfully');
        } catch (error) {
            console.error('Error initializing Google API:', error);
        }
    }

    async ensureGoogleApiSignedIn() {
        if (!this.isGoogleApiLoaded) {
            throw new Error('Google API not loaded');
        }

        const authInstance = gapi.auth2.getAuthInstance();
        
        if (!authInstance.isSignedIn.get()) {
            try {
                await authInstance.signIn();
                this.isGoogleApiSignedIn = true;
            } catch (error) {
                throw new Error('Failed to sign in to Google API: ' + error.message);
            }
        } else {
            this.isGoogleApiSignedIn = true;
        }
    }

    async processPurchase(productId) {
        try {
            // Check if user is authenticated
            if (!authManager.requireAuth()) {
                return;
            }

            const user = authManager.getCurrentUser();
            const product = PRODUCTS[productId];
            
            if (!product) {
                throw new Error('Product not found');
            }

            // Show purchase confirmation
            const confirmed = await this.showPurchaseConfirmation(product);
            if (!confirmed) return;

            // Show loading state
            this.showPurchaseLoading(true);

            // Simulate payment processing (replace with real payment gateway)
            await this.simulatePayment(product);

            // Record purchase in Firestore
            await this.recordPurchase(user, product);

            // Generate download link and send email
            await this.deliverAsset(user, product);

            this.showPurchaseSuccess(product);

        } catch (error) {
            console.error('Purchase error:', error);
            this.showPurchaseError(error.message);
        } finally {
            this.showPurchaseLoading(false);
        }
    }

    async showPurchaseConfirmation(product) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay purchase-confirmation';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Confirm Purchase</h2>
                    </div>
                    <div class="modal-body">
                        <div class="purchase-details">
                            <h3>${product.name}</h3>
                            <p>${product.description}</p>
                            <div class="price">$${product.price}</div>
                            <div class="features">
                                <h4>What's included:</h4>
                                <ul>
                                    ${product.features.map(feature => `<li>${feature}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary cancel-purchase">Cancel</button>
                        <button class="btn btn-accent confirm-purchase">Purchase for $${product.price}</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            modal.style.opacity = '1';

            modal.querySelector('.cancel-purchase').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });

            modal.querySelector('.confirm-purchase').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    resolve(false);
                }
            });
        });
    }

    async simulatePayment(product) {
        // Simulate payment processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // In a real implementation, integrate with:
        // - Stripe: https://stripe.com/docs/js
        // - PayPal: https://developer.paypal.com/docs/checkout/
        // - Ko-fi: https://ko-fi.com/manage/webhooks
        
        console.log(`Payment processed for ${product.name}: $${product.price}`);
    }

    async recordPurchase(user, product) {
        const purchaseData = {
            userId: user.uid,
            userEmail: user.email,
            productId: product.id,
            productName: product.name,
            price: product.price,
            purchaseDate: firebase.firestore.FieldValue.serverTimestamp(),
            downloadCount: 0,
            status: 'completed'
        };

        // Add to purchases collection
        const purchaseRef = await db.collection('purchases').add(purchaseData);

        // Update user document
        const userRef = db.collection('users').doc(user.uid);
        await userRef.update({
            purchases: firebase.firestore.FieldValue.arrayUnion(purchaseRef.id),
            totalSpent: firebase.firestore.FieldValue.increment(product.price)
        });

        return purchaseRef.id;
    }

    async deliverAsset(user, product) {
        try {
            // Ensure Google API is signed in
            await this.ensureGoogleApiSignedIn();

            // Create shareable link for the asset
            const downloadLink = await this.createShareableLink(product.driveFileId);

            // Send email with download link
            await this.sendPurchaseEmail(user, product, downloadLink);

        } catch (error) {
            console.error('Asset delivery error:', error);
            // Fallback: show download link in UI
            this.showDownloadLinkFallback(product);
        }
    }

    async createShareableLink(fileId) {
        try {
            // Create a permission for the file
            const permission = {
                role: 'reader',
                type: 'anyone'
            };

            await gapi.client.drive.permissions.create({
                fileId: fileId,
                resource: permission
            });

            // Get the file's web view link
            const response = await gapi.client.drive.files.get({
                fileId: fileId,
                fields: 'webViewLink,webContentLink'
            });

            return response.result.webContentLink || response.result.webViewLink;
        } catch (error) {
            console.error('Error creating shareable link:', error);
            throw new Error('Failed to create download link');
        }
    }

    async sendPurchaseEmail(user, product, downloadLink) {
        try {
            const emailContent = this.generateEmailContent(user, product, downloadLink);
            
            const message = this.createEmailMessage(
                user.email,
                EMAIL_TEMPLATES.purchase.subject.replace('{{productName}}', product.name),
                emailContent
            );

            await gapi.client.gmail.users.messages.send({
                userId: 'me',
                resource: {
                    raw: message
                }
            });

            console.log('Purchase email sent successfully');
        } catch (error) {
            console.error('Error sending email:', error);
            throw new Error('Failed to send purchase email');
        }
    }

    generateEmailContent(user, product, downloadLink) {
        const userName = user.displayName || user.email.split('@')[0];
        const featuresList = product.features.map(feature => `<li>${feature}</li>`).join('');
        
        return EMAIL_TEMPLATES.purchase.body
            .replace(/{{customerName}}/g, userName)
            .replace(/{{productName}}/g, product.name)
            .replace(/{{downloadLink}}/g, downloadLink)
            .replace(/{{featuresList}}/g, featuresList);
    }

    createEmailMessage(to, subject, htmlBody) {
        const email = [
            'Content-Type: text/html; charset="UTF-8"\n',
            'MIME-Version: 1.0\n',
            'Content-Transfer-Encoding: 7bit\n',
            `To: ${to}\n`,
            `Subject: ${subject}\n\n`,
            htmlBody
        ].join('');

        return btoa(unescape(encodeURIComponent(email))).replace(/\+/g, '-').replace(/\//g, '_');
    }

    showPurchaseLoading(show) {
        let loader = document.getElementById('purchase-loader');
        
        if (show && !loader) {
            loader = document.createElement('div');
            loader.id = 'purchase-loader';
            loader.className = 'modal-overlay';
            loader.innerHTML = `
                <div class="modal-content loading-modal">
                    <div class="loading-spinner"></div>
                    <h3>Processing your purchase...</h3>
                    <p>Please wait while we process your payment and prepare your download.</p>
                </div>
            `;
            document.body.appendChild(loader);
            loader.style.opacity = '1';
        } else if (!show && loader) {
            loader.style.opacity = '0';
            setTimeout(() => {
                if (loader.parentNode) {
                    document.body.removeChild(loader);
                }
            }, 300);
        }
    }

    showPurchaseSuccess(product) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay purchase-success';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🎉 Purchase Successful!</h2>
                </div>
                <div class="modal-body">
                    <p>Thank you for purchasing <strong>${product.name}</strong>!</p>
                    <p>A download link has been sent to your email address.</p>
                    <p>You can also access your purchases anytime from your dashboard.</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary go-to-dashboard">Go to Dashboard</button>
                    <button class="btn btn-secondary close-success">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.opacity = '1';

        modal.querySelector('.go-to-dashboard').addEventListener('click', () => {
            window.location.href = 'dashboard.html';
        });

        modal.querySelector('.close-success').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    showPurchaseError(message) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay purchase-error';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>❌ Purchase Failed</h2>
                </div>
                <div class="modal-body">
                    <p>Sorry, there was an error processing your purchase:</p>
                    <p class="error-message">${message}</p>
                    <p>Please try again or contact support if the problem persists.</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary close-error">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.opacity = '1';

        modal.querySelector('.close-error').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    showDownloadLinkFallback(product) {
        alert(`Your purchase was successful! However, we couldn't automatically send the email. Please contact support with your purchase details to receive your download link for ${product.name}.`);
    }
}

// Initialize purchase manager
const purchaseManager = new PurchaseManager();

// Export for global access
window.purchaseManager = purchaseManager;
