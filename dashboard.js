// Dashboard Management System
class DashboardManager {
    constructor() {
        this.currentUser = null;
        this.userPurchases = [];
        this.userStats = {
            totalPurchases: 0,
            totalSpent: 0,
            totalDownloads: 0
        };
        
        this.initializeDashboard();
        this.setupEventListeners();
    }

    initializeDashboard() {
        // Check authentication state
        auth.onAuthStateChanged((user) => {
            if (user) {
                this.currentUser = user;
                this.loadUserData();
                this.updateUserDisplay();
            } else {
                // Redirect to home if not authenticated
                window.location.href = 'index.html';
            }
        });
    }

    setupEventListeners() {
        // Navigation
        document.getElementById('logout-btn').addEventListener('click', () => {
            authManager.signOut();
        });

        document.getElementById('browse-more-btn').addEventListener('click', () => {
            window.location.href = 'index.html#featured-section';
        });

        document.getElementById('shop-now-btn').addEventListener('click', () => {
            window.location.href = 'index.html#featured-section';
        });

        // Modal controls
        document.getElementById('asset-modal-close').addEventListener('click', () => {
            this.hideAssetModal();
        });

        document.getElementById('asset-modal').addEventListener('click', (e) => {
            if (e.target.id === 'asset-modal') {
                this.hideAssetModal();
            }
        });

        // Asset actions
        document.getElementById('download-asset-btn').addEventListener('click', () => {
            this.downloadAsset();
        });

        document.getElementById('redownload-btn').addEventListener('click', () => {
            this.resendDownloadEmail();
        });
    }

    updateUserDisplay() {
        const displayName = this.currentUser.displayName || this.currentUser.email.split('@')[0];
        document.getElementById('user-display-name').textContent = displayName;
    }

    async loadUserData() {
        try {
            this.showLoadingState(true);
            
            // Load user document
            const userDoc = await db.collection('users').doc(this.currentUser.uid).get();
            
            if (userDoc.exists) {
                const userData = userDoc.data();
                this.userStats.totalSpent = userData.totalSpent || 0;
                
                // Load user purchases
                await this.loadUserPurchases();
                
                // Update UI
                this.updateStatsDisplay();
                this.renderPurchasedAssets();
                this.loadRecentActivity();
            }
            
        } catch (error) {
            console.error('Error loading user data:', error);
            this.showError('Failed to load your data. Please refresh the page.');
        } finally {
            this.showLoadingState(false);
        }
    }

    async loadUserPurchases() {
        try {
            const purchasesQuery = await db.collection('purchases')
                .where('userId', '==', this.currentUser.uid)
                .orderBy('purchaseDate', 'desc')
                .get();

            this.userPurchases = purchasesQuery.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            this.userStats.totalPurchases = this.userPurchases.length;
            this.userStats.totalDownloads = this.userPurchases.reduce((total, purchase) => {
                return total + (purchase.downloadCount || 0);
            }, 0);

        } catch (error) {
            console.error('Error loading purchases:', error);
            this.userPurchases = [];
        }
    }

    updateStatsDisplay() {
        document.getElementById('total-purchases').textContent = this.userStats.totalPurchases;
        document.getElementById('total-spent').textContent = `$${this.userStats.totalSpent}`;
        document.getElementById('total-downloads').textContent = this.userStats.totalDownloads;
    }

    renderPurchasedAssets() {
        const assetsGrid = document.getElementById('assets-grid');
        const emptyState = document.getElementById('empty-state');

        if (this.userPurchases.length === 0) {
            assetsGrid.innerHTML = '';
            assetsGrid.appendChild(emptyState);
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        assetsGrid.innerHTML = '';

        this.userPurchases.forEach(purchase => {
            const product = PRODUCTS[purchase.productId];
            if (!product) return;

            const assetCard = this.createAssetCard(purchase, product);
            assetsGrid.appendChild(assetCard);
        });
    }

    createAssetCard(purchase, product) {
        const card = document.createElement('div');
        card.className = 'asset-card';
        card.dataset.purchaseId = purchase.id;

        const purchaseDate = purchase.purchaseDate ? 
            new Date(purchase.purchaseDate.seconds * 1000).toLocaleDateString() : 
            'Unknown';

        card.innerHTML = `
            <div class="asset-thumbnail">
                <img src="https://via.placeholder.com/320x180/${this.getProductColor(product.id)}/ffffff?text=${encodeURIComponent(product.name)}" 
                     alt="${product.name}">
                <div class="asset-status">Owned</div>
            </div>
            <div class="asset-content">
                <h3 class="asset-title">${product.name}</h3>
                <p class="asset-description">${product.description}</p>
                <div class="asset-meta">
                    <span class="asset-price">$${product.price}</span>
                    <span class="asset-date">${purchaseDate}</span>
                </div>
                <div class="asset-actions">
                    <button class="btn btn-primary btn-small view-details-btn">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <button class="btn btn-accent btn-small download-btn">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        card.querySelector('.view-details-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.showAssetModal(purchase, product);
        });

        card.querySelector('.download-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.downloadAsset(purchase, product);
        });

        card.addEventListener('click', () => {
            this.showAssetModal(purchase, product);
        });

        return card;
    }

    getProductColor(productId) {
        const colors = {
            'combat-system-v2': '4444ff',
            'admin-panel-pro': 'ff4444',
            'economy-system': '44ff44',
            'map-builder-tool': 'ff8844'
        };
        return colors[productId] || 'ff4444';
    }

    showAssetModal(purchase, product) {
        const modal = document.getElementById('asset-modal');
        const purchaseDate = purchase.purchaseDate ? 
            new Date(purchase.purchaseDate.seconds * 1000).toLocaleDateString() : 
            'Unknown';

        // Update modal content
        document.getElementById('asset-modal-title').textContent = product.name;
        document.getElementById('asset-modal-image').src = 
            `https://via.placeholder.com/200x150/${this.getProductColor(product.id)}/ffffff?text=${encodeURIComponent(product.name)}`;
        document.getElementById('asset-modal-image').alt = product.name;
        document.getElementById('asset-modal-name').textContent = product.name;
        document.getElementById('asset-modal-description').textContent = product.description;
        document.getElementById('asset-modal-date').textContent = purchaseDate;
        document.getElementById('asset-modal-price').textContent = `$${product.price}`;
        document.getElementById('asset-modal-downloads').textContent = purchase.downloadCount || 0;

        // Update features list
        const featuresList = document.getElementById('asset-modal-features');
        featuresList.innerHTML = '';
        product.features.forEach(feature => {
            const li = document.createElement('li');
            li.textContent = feature;
            featuresList.appendChild(li);
        });

        // Store current asset data
        modal.dataset.purchaseId = purchase.id;
        modal.dataset.productId = product.id;

        // Show modal
        modal.style.display = 'flex';
        setTimeout(() => {
            modal.style.opacity = '1';
        }, 10);
    }

    hideAssetModal() {
        const modal = document.getElementById('asset-modal');
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    async downloadAsset(purchase = null, product = null) {
        try {
            // Get asset info from modal if not provided
            if (!purchase || !product) {
                const modal = document.getElementById('asset-modal');
                const purchaseId = modal.dataset.purchaseId;
                const productId = modal.dataset.productId;
                
                purchase = this.userPurchases.find(p => p.id === purchaseId);
                product = PRODUCTS[productId];
            }

            if (!purchase || !product) {
                throw new Error('Asset information not found');
            }

            // Increment download count
            await this.incrementDownloadCount(purchase.id);

            // Create download link (in real implementation, this would be a secure, time-limited link)
            const downloadUrl = `https://drive.google.com/file/d/${product.driveFileId}/view`;
            
            // Open download in new tab
            window.open(downloadUrl, '_blank');

            this.showSuccess('Download started! The file will open in a new tab.');

        } catch (error) {
            console.error('Download error:', error);
            this.showError('Failed to download asset. Please try again.');
        }
    }

    async resendDownloadEmail() {
        try {
            const modal = document.getElementById('asset-modal');
            const purchaseId = modal.dataset.purchaseId;
            const productId = modal.dataset.productId;
            
            const purchase = this.userPurchases.find(p => p.id === purchaseId);
            const product = PRODUCTS[productId];

            if (!purchase || !product) {
                throw new Error('Asset information not found');
            }

            // Use purchase manager to resend email
            await purchaseManager.deliverAsset(this.currentUser, product);
            
            this.showSuccess('Download link has been sent to your email!');

        } catch (error) {
            console.error('Email resend error:', error);
            this.showError('Failed to send email. Please try again.');
        }
    }

    async incrementDownloadCount(purchaseId) {
        try {
            await db.collection('purchases').doc(purchaseId).update({
                downloadCount: firebase.firestore.FieldValue.increment(1)
            });

            // Update local data
            const purchase = this.userPurchases.find(p => p.id === purchaseId);
            if (purchase) {
                purchase.downloadCount = (purchase.downloadCount || 0) + 1;
                this.userStats.totalDownloads++;
                this.updateStatsDisplay();
            }

        } catch (error) {
            console.error('Error updating download count:', error);
        }
    }

    async loadRecentActivity() {
        const activityList = document.getElementById('activity-list');
        
        if (this.userPurchases.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-info"></i>
                    </div>
                    <div class="activity-content">
                        <h4 class="activity-title">Welcome to RobloxDevX!</h4>
                        <p class="activity-description">Start by browsing our collection of Roblox assets and systems.</p>
                    </div>
                    <div class="activity-time">Just now</div>
                </div>
            `;
            return;
        }

        activityList.innerHTML = '';
        
        // Show recent purchases
        this.userPurchases.slice(0, 5).forEach(purchase => {
            const product = PRODUCTS[purchase.productId];
            if (!product) return;

            const purchaseDate = purchase.purchaseDate ? 
                new Date(purchase.purchaseDate.seconds * 1000) : 
                new Date();

            const timeAgo = this.getTimeAgo(purchaseDate);

            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="activity-content">
                    <h4 class="activity-title">Purchased ${product.name}</h4>
                    <p class="activity-description">Successfully purchased for $${product.price}</p>
                </div>
                <div class="activity-time">${timeAgo}</div>
            `;

            activityList.appendChild(activityItem);
        });
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        return `${Math.floor(diffInSeconds / 86400)} days ago`;
    }

    showLoadingState(show) {
        const loadingState = document.getElementById('loading-state');
        if (show) {
            loadingState.style.display = 'block';
        } else {
            loadingState.style.display = 'none';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const dashboardManager = new DashboardManager();
    window.dashboardManager = dashboardManager;
});
