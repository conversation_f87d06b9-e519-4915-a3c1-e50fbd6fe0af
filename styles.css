/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Profile Section */
.profile-section {
    text-align: center;
    padding: 80px 0;
    border-bottom: 1px solid #1a1a1a;
}

.avatar {
    margin-bottom: 24px;
}

.avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px solid #ff4444;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.avatar img:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 68, 68, 0.3);
}

.username {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ff4444, #4444ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    font-size: 1.25rem;
    color: #cccccc;
    margin-bottom: 32px;
    font-weight: 400;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: #1a1a1a;
    border: 2px solid #333;
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 68, 68, 0.2);
}

.social-link.discord:hover {
    border-color: #5865f2;
    background: #5865f2;
}

.social-link.github:hover {
    border-color: #ffffff;
    background: #ffffff;
    color: #0a0a0a;
}

.social-link.roblox:hover {
    border-color: #00a2ff;
    background: #00a2ff;
}

.social-link.kofi:hover {
    border-color: #ff5f5f;
    background: #ff5f5f;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 48px;
    color: #ffffff;
}

/* Featured Section */
.featured-section {
    padding: 80px 0;
    border-bottom: 1px solid #1a1a1a;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-top: 48px;
}

.product-card {
    background: #111111;
    border: 1px solid #222;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-8px);
    border-color: #ff4444;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.product-thumbnail {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-thumbnail img {
    transform: scale(1.05);
}

.product-info {
    padding: 24px;
}

.product-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: #ffffff;
}

.product-description {
    color: #cccccc;
    margin-bottom: 20px;
    line-height: 1.5;
}

.product-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #4444ff;
}

.btn-primary:hover {
    background: #4444ff;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(68, 68, 255, 0.3);
}

.btn-secondary {
    background: #ff4444;
    color: #ffffff;
    border: 2px solid #ff4444;
}

.btn-secondary:hover {
    background: #ff3333;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 68, 68, 0.3);
}

.btn-accent {
    background: linear-gradient(135deg, #ff4444, #4444ff);
    color: #ffffff;
    border: none;
    font-size: 1.1rem;
    padding: 16px 32px;
}

.btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(255, 68, 68, 0.3);
}

/* Commissions Section */
.commissions-section {
    padding: 80px 0;
    text-align: center;
    border-bottom: 1px solid #1a1a1a;
}

.commission-content {
    max-width: 600px;
    margin: 0 auto;
}

.commission-text {
    font-size: 1.25rem;
    color: #cccccc;
    margin-bottom: 32px;
}

.commission-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.feature i {
    font-size: 2rem;
    color: #ff4444;
}

.feature span {
    color: #cccccc;
    font-weight: 600;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    border-bottom: 1px solid #1a1a1a;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-top: 48px;
}

.testimonial {
    background: #111111;
    padding: 32px;
    border-radius: 16px;
    border: 1px solid #222;
    text-align: center;
}

.testimonial p {
    font-size: 1.1rem;
    color: #ffffff;
    margin-bottom: 16px;
    font-style: italic;
}

.testimonial-author {
    color: #ff4444;
    font-weight: 600;
}

/* Authentication Section */
.auth-section {
    margin-top: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
}

.auth-buttons {
    display: flex;
    gap: 12px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;
}

.welcome-text {
    color: #cccccc;
    font-weight: 600;
}

.btn-outline {
    background: transparent;
    color: #cccccc;
    border: 2px solid #333;
}

.btn-outline:hover {
    background: #333;
    color: #ffffff;
    transform: translateY(-2px);
}

/* Authentication Modal */
.auth-modal {
    max-width: 400px;
    width: 90%;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #cccccc;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    background: #1a1a1a;
    border: 2px solid #333;
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #ff4444;
}

.auth-divider {
    text-align: center;
    margin: 24px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #333;
}

.auth-divider span {
    background: #111;
    padding: 0 16px;
    color: #666;
    position: relative;
}

.btn-google {
    width: 100%;
    background: #4285f4;
    color: #ffffff;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background 0.3s ease;
}

.btn-google:hover {
    background: #3367d6;
    transform: translateY(-2px);
}

.auth-switch {
    text-align: center;
    margin-top: 24px;
    color: #cccccc;
}

.auth-switch a {
    color: #ff4444;
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

.auth-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 600;
}

.auth-message-error {
    background: rgba(255, 68, 68, 0.1);
    border: 1px solid #ff4444;
    color: #ff4444;
}

.auth-message-success {
    background: rgba(68, 255, 68, 0.1);
    border: 1px solid #44ff44;
    color: #44ff44;
}

.auth-message-warning {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid #ffa500;
    color: #ffa500;
}

/* Purchase Modal Styles */
.purchase-confirmation .modal-content,
.purchase-success .modal-content,
.purchase-error .modal-content {
    max-width: 500px;
}

.purchase-details {
    text-align: center;
}

.purchase-details .price {
    font-size: 2rem;
    font-weight: 800;
    color: #ff4444;
    margin: 16px 0;
}

.purchase-details .features {
    text-align: left;
    margin-top: 24px;
}

.purchase-details .features ul {
    list-style: none;
    padding: 0;
}

.purchase-details .features li {
    padding: 4px 0;
    color: #cccccc;
}

.purchase-details .features li:before {
    content: "✓ ";
    color: #44ff44;
    font-weight: bold;
    margin-right: 8px;
}

.loading-modal {
    text-align: center;
    padding: 40px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #333;
    border-top: 4px solid #ff4444;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 24px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: rgba(255, 68, 68, 0.1);
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 12px;
    border-radius: 8px;
    margin: 16px 0;
}

/* Footer */
.footer {
    padding: 60px 0 40px;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
}

.footer-links {
    display: flex;
    gap: 32px;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-links a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff4444;
}

.footer-social {
    display: flex;
    gap: 16px;
}

.footer-text {
    color: #666666;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .username {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .commission-features {
        gap: 24px;
    }
    
    .product-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }
    
    .profile-section,
    .featured-section,
    .commissions-section,
    .testimonials-section {
        padding: 60px 0;
    }
    
    .username {
        font-size: 2rem;
    }
    
    .social-links {
        gap: 12px;
    }
    
    .social-link {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }
}
