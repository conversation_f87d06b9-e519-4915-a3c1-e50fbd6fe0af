<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - RobloxDevX</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Google APIs -->
    <script src="https://apis.google.com/js/api.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Navigation -->
        <nav class="dashboard-nav">
            <div class="nav-brand">
                <a href="index.html">
                    <h2>RobloxDevX</h2>
                </a>
            </div>
            <div class="nav-user">
                <span class="user-greeting">Welcome, <span id="user-display-name">User</span>!</span>
                <button class="btn btn-outline" id="logout-btn">Logout</button>
            </div>
        </nav>

        <!-- Dashboard Content -->
        <main class="dashboard-main">
            <!-- User Stats -->
            <section class="user-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-purchases">0</h3>
                        <p>Total Purchases</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-spent">$0</h3>
                        <p>Total Spent</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-downloads">0</h3>
                        <p>Total Downloads</p>
                    </div>
                </div>
            </section>

            <!-- Purchased Assets -->
            <section class="purchased-assets">
                <div class="section-header">
                    <h2>Your Purchased Assets</h2>
                    <button class="btn btn-primary" id="browse-more-btn">Browse More Assets</button>
                </div>
                
                <div class="assets-grid" id="assets-grid">
                    <!-- Loading state -->
                    <div class="loading-state" id="loading-state">
                        <div class="loading-spinner"></div>
                        <p>Loading your assets...</p>
                    </div>
                    
                    <!-- Empty state -->
                    <div class="empty-state" id="empty-state" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h3>No purchases yet</h3>
                        <p>Start building your collection of Roblox assets!</p>
                        <button class="btn btn-accent" id="shop-now-btn">Shop Now</button>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="recent-activity">
                <h2>Recent Activity</h2>
                <div class="activity-list" id="activity-list">
                    <!-- Activity items will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Asset Details Modal -->
    <div id="asset-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content asset-modal">
            <div class="modal-header">
                <h2 id="asset-modal-title">Asset Details</h2>
                <button class="modal-close" id="asset-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="asset-details">
                    <div class="asset-thumbnail">
                        <img id="asset-modal-image" src="" alt="">
                    </div>
                    <div class="asset-info">
                        <h3 id="asset-modal-name"></h3>
                        <p id="asset-modal-description"></p>
                        <div class="asset-meta">
                            <div class="meta-item">
                                <strong>Purchase Date:</strong>
                                <span id="asset-modal-date"></span>
                            </div>
                            <div class="meta-item">
                                <strong>Price Paid:</strong>
                                <span id="asset-modal-price"></span>
                            </div>
                            <div class="meta-item">
                                <strong>Downloads:</strong>
                                <span id="asset-modal-downloads"></span>
                            </div>
                        </div>
                        <div class="asset-features">
                            <h4>Features:</h4>
                            <ul id="asset-modal-features"></ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-accent" id="download-asset-btn">
                    <i class="fas fa-download"></i> Download Asset
                </button>
                <button class="btn btn-secondary" id="redownload-btn">
                    <i class="fas fa-envelope"></i> Email Download Link
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="purchase.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
