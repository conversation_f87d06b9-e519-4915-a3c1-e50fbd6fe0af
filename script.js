// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to all links
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Product card interactions
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Button click handlers
    const viewButtons = document.querySelectorAll('.btn-primary');
    const buyButtons = document.querySelectorAll('.btn-secondary');
    const commissionBtn = document.querySelector('.commission-btn');

    viewButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const productCard = this.closest('.product-card');
            const productTitle = productCard.querySelector('.product-title').textContent;
            
            // Create modal or redirect to product page
            showProductModal(productTitle, productCard);
        });
    });

    buyButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const productCard = this.closest('.product-card');
            const productTitle = productCard.querySelector('.product-title').textContent;
            const price = this.textContent.match(/\$\d+/)[0];
            
            // Simulate purchase process
            handlePurchase(productTitle, price);
        });
    });

    if (commissionBtn) {
        commissionBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openCommissionForm();
        });
    }

    // Social link interactions
    const socialLinks = document.querySelectorAll('.social-link');
    
    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const platform = this.classList[1]; // Get the platform class
            handleSocialClick(platform);
        });
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for scroll animations
    const animatedElements = document.querySelectorAll('.product-card, .testimonial, .section-title');
    
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Avatar click easter egg
    const avatar = document.getElementById('avatar-img');
    let clickCount = 0;
    
    avatar.addEventListener('click', function() {
        clickCount++;
        
        if (clickCount === 5) {
            this.style.transform = 'rotate(360deg) scale(1.2)';
            this.style.transition = 'transform 1s ease';
            
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                clickCount = 0;
            }, 1000);
        }
    });
});

// Product modal function
function showProductModal(productTitle, productCard) {
    const description = productCard.querySelector('.product-description').textContent;
    const thumbnail = productCard.querySelector('.product-thumbnail img').src;
    
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${productTitle}</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <img src="${thumbnail}" alt="${productTitle}" class="modal-image">
                <p>${description}</p>
                <div class="modal-features">
                    <h3>Features:</h3>
                    <ul>
                        <li>Clean, optimized code</li>
                        <li>Full documentation included</li>
                        <li>30-day support</li>
                        <li>Easy installation</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary modal-buy">Purchase Now</button>
                <button class="btn btn-primary modal-demo">View Demo</button>
            </div>
        </div>
    `;
    
    // Add modal styles
    const modalStyles = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .modal-content {
            background: #111;
            border: 1px solid #333;
            border-radius: 16px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-bottom: 1px solid #333;
        }
        .modal-close {
            background: none;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
        }
        .modal-body {
            padding: 24px;
        }
        .modal-image {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        .modal-features ul {
            list-style: none;
            padding-left: 0;
        }
        .modal-features li {
            padding: 4px 0;
            color: #ccc;
        }
        .modal-features li:before {
            content: "✓ ";
            color: #ff4444;
            font-weight: bold;
        }
        .modal-footer {
            padding: 24px;
            border-top: 1px solid #333;
            display: flex;
            gap: 12px;
        }
    `;
    
    // Add styles to head if not already added
    if (!document.querySelector('#modal-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'modal-styles';
        styleSheet.textContent = modalStyles;
        document.head.appendChild(styleSheet);
    }
    
    document.body.appendChild(modal);
    
    // Animate in
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);
    
    // Close modal handlers
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });
    
    function closeModal() {
        modal.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }
}

// Purchase handler
function handlePurchase(productTitle, price) {
    alert(`Redirecting to payment for ${productTitle} (${price})...\n\nIn a real implementation, this would redirect to your payment processor (PayPal, Stripe, Ko-fi, etc.)`);
}

// Commission form handler
function openCommissionForm() {
    alert('Opening commission form...\n\nIn a real implementation, this would open a contact form or redirect to your preferred commission platform.');
}

// Social link handler
function handleSocialClick(platform) {
    const socialUrls = {
        discord: 'https://discord.gg/your-server',
        github: 'https://github.com/yourusername',
        roblox: 'https://www.roblox.com/users/your-user-id/profile',
        kofi: 'https://ko-fi.com/yourusername'
    };
    
    if (socialUrls[platform]) {
        window.open(socialUrls[platform], '_blank');
    }
}

// Parallax effect for background
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.profile-section');
    const speed = scrolled * 0.5;
    
    if (parallax) {
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Loading animation
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
