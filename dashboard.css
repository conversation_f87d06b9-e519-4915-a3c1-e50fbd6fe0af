/* Dashboard Specific Styles */

.dashboard-container {
    min-height: 100vh;
    background: #0a0a0a;
}

/* Navigation */
.dashboard-nav {
    background: #111111;
    border-bottom: 1px solid #222;
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.dashboard-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand h2 {
    color: #ffffff;
    margin: 0;
    background: linear-gradient(135deg, #ff4444, #4444ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-brand a {
    text-decoration: none;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-greeting {
    color: #cccccc;
    font-weight: 600;
}

/* Dashboard Main */
.dashboard-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* User Stats */
.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 48px;
}

.stat-card {
    background: #111111;
    border: 1px solid #222;
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: #ff4444;
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff4444, #4444ff);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ffffff;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 800;
    color: #ffffff;
    margin: 0 0 4px 0;
}

.stat-info p {
    color: #cccccc;
    margin: 0;
    font-weight: 600;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    flex-wrap: wrap;
    gap: 16px;
}

.section-header h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

/* Assets Grid */
.assets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 48px;
}

.asset-card {
    background: #111111;
    border: 1px solid #222;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.asset-card:hover {
    transform: translateY(-4px);
    border-color: #ff4444;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.asset-thumbnail {
    width: 100%;
    height: 180px;
    overflow: hidden;
    position: relative;
}

.asset-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.asset-card:hover .asset-thumbnail img {
    transform: scale(1.05);
}

.asset-status {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #44ff44;
    color: #000;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-content {
    padding: 20px;
}

.asset-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 8px 0;
}

.asset-description {
    color: #cccccc;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.asset-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 0.9rem;
}

.asset-price {
    color: #ff4444;
    font-weight: 700;
}

.asset-date {
    color: #888;
}

.asset-actions {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 6px;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    font-size: 4rem;
    color: #333;
    margin-bottom: 24px;
}

.empty-state h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 12px;
}

.empty-state p {
    color: #cccccc;
    margin-bottom: 24px;
}

/* Recent Activity */
.recent-activity {
    margin-top: 48px;
}

.recent-activity h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 24px;
}

.activity-list {
    background: #111111;
    border: 1px solid #222;
    border-radius: 16px;
    overflow: hidden;
}

.activity-item {
    padding: 16px 20px;
    border-bottom: 1px solid #222;
    display: flex;
    align-items: center;
    gap: 16px;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #ff4444;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: #ffffff;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.activity-description {
    color: #cccccc;
    font-size: 0.9rem;
    margin: 0;
}

.activity-time {
    color: #888;
    font-size: 0.85rem;
}

/* Asset Modal */
.asset-modal {
    max-width: 700px;
    width: 95%;
}

.asset-details {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 24px;
    align-items: start;
}

.asset-details .asset-thumbnail {
    width: 200px;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
}

.asset-details .asset-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.asset-info h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin: 0 0 12px 0;
}

.asset-info p {
    color: #cccccc;
    margin: 0 0 20px 0;
}

.asset-meta {
    margin-bottom: 20px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: #cccccc;
}

.meta-item strong {
    color: #ffffff;
}

.asset-features h4 {
    color: #ffffff;
    margin: 0 0 12px 0;
}

.asset-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.asset-features li {
    color: #cccccc;
    padding: 4px 0;
}

.asset-features li:before {
    content: "✓ ";
    color: #44ff44;
    font-weight: bold;
    margin-right: 8px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    color: #ffffff;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #44ff44;
    color: #000000;
}

.notification-error {
    background: #ff4444;
    color: #ffffff;
}

.notification-info {
    background: #4444ff;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-nav .container {
        flex-direction: column;
        gap: 16px;
    }

    .nav-user {
        flex-direction: column;
        gap: 12px;
    }

    .user-stats {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .assets-grid {
        grid-template-columns: 1fr;
    }

    .asset-details {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .asset-details .asset-thumbnail {
        width: 100%;
        height: 200px;
        margin: 0 auto;
    }

    .meta-item {
        justify-content: center;
        gap: 8px;
    }
}
