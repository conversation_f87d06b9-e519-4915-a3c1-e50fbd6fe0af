// Firebase Configuration
// Replace these with your actual Firebase config values
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};

// Google API Configuration
const GOOGLE_CONFIG = {
    // Replace with your Google Cloud Project API Key
    API_KEY: 'your-google-api-key-here',
    
    // Replace with your Google Cloud Project Client ID
    CLIENT_ID: 'your-google-client-id.apps.googleusercontent.com',
    
    // Required scopes for Drive and Gmail APIs
    DISCOVERY_DOCS: [
        'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
        'https://www.googleapis.com/discovery/v1/apis/gmail/v1/rest'
    ],
    
    SCOPES: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/gmail.send'
    ].join(' ')
};

// Product Configuration
const PRODUCTS = {
    'combat-system-v2': {
        id: 'combat-system-v2',
        name: 'Combat System V2',
        price: 25,
        description: 'Advanced PvP combat system with combos, blocking, and special abilities',
        driveFileId: 'your-drive-file-id-1', // Replace with actual Google Drive file ID
        features: [
            'Advanced combo system',
            'Block and parry mechanics',
            'Special abilities framework',
            'Damage calculation system',
            'Animation integration',
            'Full documentation'
        ]
    },
    'admin-panel-pro': {
        id: 'admin-panel-pro',
        name: 'Admin Panel Pro',
        price: 35,
        description: 'Complete admin system with GUI, commands, and permission levels',
        driveFileId: 'your-drive-file-id-2',
        features: [
            'Modern GUI interface',
            'Command system',
            'Permission levels',
            'Player management',
            'Server controls',
            'Logging system'
        ]
    },
    'economy-system': {
        id: 'economy-system',
        name: 'Economy System',
        price: 40,
        description: 'Full economy with shops, currency, trading, and data persistence',
        driveFileId: 'your-drive-file-id-3',
        features: [
            'Currency management',
            'Shop system',
            'Trading mechanics',
            'Data persistence',
            'Transaction logging',
            'Anti-exploit protection'
        ]
    },
    'map-builder-tool': {
        id: 'map-builder-tool',
        name: 'Map Builder Tool',
        price: 30,
        description: 'In-game map creation tool with save/load and sharing features',
        driveFileId: 'your-drive-file-id-4',
        features: [
            'Drag and drop building',
            'Save/load maps',
            'Sharing system',
            'Terrain tools',
            'Object library',
            'Collaboration features'
        ]
    }
};

// Email Templates
const EMAIL_TEMPLATES = {
    purchase: {
        subject: 'Your Roblox Asset Purchase - {{productName}}',
        body: `
            <h2>Thank you for your purchase!</h2>
            <p>Hi {{customerName}},</p>
            <p>Thank you for purchasing <strong>{{productName}}</strong> from RobloxDevX!</p>
            
            <h3>Download Information:</h3>
            <p>You can download your asset using the link below:</p>
            <p><a href="{{downloadLink}}" style="background: #ff4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px;">Download {{productName}}</a></p>
            
            <h3>What's Included:</h3>
            <ul>
                {{featuresList}}
            </ul>
            
            <h3>Installation Instructions:</h3>
            <ol>
                <li>Download the asset files from the link above</li>
                <li>Extract the files to your Roblox Studio workspace</li>
                <li>Follow the included documentation for setup</li>
                <li>Contact support if you need assistance</li>
            </ol>
            
            <h3>Support:</h3>
            <p>If you have any questions or need help with installation, please contact us:</p>
            <ul>
                <li>Discord: RobloxDevX#1234</li>
                <li>Email: <EMAIL></li>
            </ul>
            
            <p>Thank you for choosing RobloxDevX!</p>
            <p>Best regards,<br>The RobloxDevX Team</p>
            
            <hr>
            <p style="font-size: 12px; color: #666;">
                This email was sent because you purchased an asset from RobloxDevX. 
                Please keep this email for your records.
            </p>
        `
    }
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Export for use in other files
window.firebaseConfig = firebaseConfig;
window.GOOGLE_CONFIG = GOOGLE_CONFIG;
window.PRODUCTS = PRODUCTS;
window.EMAIL_TEMPLATES = EMAIL_TEMPLATES;
window.auth = auth;
window.db = db;
